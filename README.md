# 三三制查经 - LLM Chat Bot

A chatbot service using LangChain, OpenRouter, and FastAPI that provides an OpenAI-compatible API interface.

## Features

- OpenAI-compatible API for easy integration with existing tools
- Support for multiple LLM models through OpenRouter
- Support for both shorthand model names and raw model IDs
- Models discovery endpoint for available model information
- Customizable system message
- Authentication with API keys

## Prerequisites

- Python 3.10+
- Poetry for dependency management
- OpenRouter API key

## Local Development

### Setup

1. Clone the repository:

```bash
git clone https://github.com/ExcelsisCN/triple3-chat.git
cd triple3-chat
```

2. Install dependencies with Poetry:

```bash
poetry install
```

3. Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

4. Edit the `.env` file with your OpenRouter API key and other settings.

### Running Locally

```bash
poetry run python -m src.chat.main
```

The API will be available at http://localhost:8000/api/v1/

### Running Tests

```bash
poetry run pytest
```

## API Usage

### Authentication

Add your API key using HTTP Bearer authentication in your requests:

```
Authorization: Bearer your-api-key
```

The API key should match one of the keys defined in your `.env` file under the `API_KEYS` setting.

### List Available Models

To retrieve the list of available models and their details:

```http
GET /api/v1/models
Authorization: Bearer your-api-key
```

Example response:

```json
{
  "models": {
    "claude-3.5-sonnet": {
      "id": "anthropic/claude-3.5-sonnet-20240620",
      "provider": "anthropic",
      "shorthand": "claude-3.5-sonnet",
      "supports_streaming": true
    },
    "gpt-4.1": {
      "id": "openai/gpt-4.1",
      "provider": "openai",
      "shorthand": "gpt-4.1",
      "supports_streaming": true
    },
    // other models...
  }
}
```

### Chat Completion

```http
POST /api/v1/chat/completions
Content-Type: application/json
Authorization: Bearer your-api-key

{
  "model": "deepseek-v3",  // Shorthand model name
  "messages": [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Hello, how are you?"}
  ],
  "temperature": 0.7,
  "max_tokens": 1000
}
```

### Streaming Chat Completion

The API supports real-time streaming of chat completions using Server-Sent Events (SSE). This allows tokens to be sent incrementally as they are generated, providing a better user experience for long responses.

#### Basic Streaming Request

```http
POST /api/v1/chat/completions
Content-Type: application/json
Authorization: Bearer your-api-key

{
  "model": "deepseek-v3",
  "messages": [
    {"role": "user", "content": "Write a short story about a robot."}
  ],
  "stream": true,
  "temperature": 0.7,
  "max_tokens": 1000
}
```

#### Streaming Response Format

The response will be sent as Server-Sent Events with `Content-Type: text/event-stream`. Each chunk follows this format:

```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"deepseek-v3","choices":[{"index":0,"delta":{"role":"assistant"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"deepseek-v3","choices":[{"index":0,"delta":{"content":"Once"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"deepseek-v3","choices":[{"index":0,"delta":{"content":" upon"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"deepseek-v3","choices":[{"index":0,"delta":{"content":" a"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"deepseek-v3","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}

data: [DONE]
```

#### Streaming with curl

```bash
curl -X POST http://localhost:8000/api/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-api-key" \
  -d '{
    "model": "deepseek-v3",
    "messages": [
      {"role": "user", "content": "Write a short story about a robot."}
    ],
    "stream": true,
    "temperature": 0.7,
    "max_tokens": 500
  }' \
  --no-buffer
```

#### Using Raw Model IDs

You can also use raw model IDs directly instead of shorthand names:

```http
POST /api/v1/chat/completions
Content-Type: application/json
Authorization: Bearer your-api-key

{
  "model": "openai/gpt-4.1",  // Raw model ID with provider prefix
  "messages": [
    {"role": "user", "content": "Hello, how are you?"}
  ],
  "temperature": 0.7,
  "max_tokens": 1000
}
```

### Using the API on Localhost

When running the API locally, you can test it using curl or any HTTP client:

#### Health Check

```bash
curl -X GET http://localhost:8000/api/v1/health
```

Expected response:
```json
{"status":"ok","version":"v1"}
```

#### Retrieving Available Models with curl

```bash
curl -X GET http://localhost:8000/api/v1/models \
  -H "Authorization: Bearer test-api-key"
```

#### Chat Completion with curl (using shorthand model name)

```bash
curl -X POST http://localhost:8000/api/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-api-key" \
  -d '{
    "model": "deepseek-v3",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "temperature": 0.7,
    "max_tokens": 100
  }'
```

#### Chat Completion with curl (using raw model ID)

```bash
curl -X POST http://localhost:8000/api/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-api-key" \
  -d '{
    "model": "openai/gpt-4.1",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "temperature": 0.7,
    "max_tokens": 100
  }'
```

#### Multi-turn Conversation Example

For multi-turn conversations, include the entire conversation history in the messages array:

```bash
curl -X POST http://localhost:8000/api/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-api-key" \
  -d '{
    "model": "deepseek-v3",
    "messages": [
      {"role": "system", "content": "You are a helpful assistant who specializes in Bible study."},
      {"role": "user", "content": "Hello, I would like to study the book of John."},
      {"role": "assistant", "content": "That'\''s a great choice! The Gospel of John is one of the four canonical gospels. Would you like to focus on a specific chapter?"},
      {"role": "user", "content": "Let'\''s start with John chapter 1. What are the key themes?"}
    ],
    "temperature": 0.7,
    "max_tokens": 500
  }'
```

### Python Client Examples

#### For Localhost

```python
import requests

api_key = "test-api-key"  # Use the API key from your .env file
base_url = "http://localhost:8000/api/v1"

# Get available models
def get_models():
    response = requests.get(
        f"{base_url}/models",
        headers={"Authorization": f"Bearer {api_key}"}
    )
    return response.json()

# Chat completion with shorthand model name
def chat_with_shorthand_model():
    response = requests.post(
        f"{base_url}/chat/completions",
        headers={"Authorization": f"Bearer {api_key}"},
        json={
            "model": "deepseek-v3",  # Shorthand model name
            "messages": [
                {"role": "user", "content": "Hello, how are you?"}
            ],
            "temperature": 0.7,
            "max_tokens": 100
        },
    )
    return response.json()

# Chat completion with raw model ID
def chat_with_raw_model_id():
    response = requests.post(
        f"{base_url}/chat/completions",
        headers={"Authorization": f"Bearer {api_key}"},
        json={
            "model": "openai/gpt-4.1",  # Raw model ID with provider prefix
            "messages": [
                {"role": "user", "content": "Hello, how are you?"}
            ],
            "temperature": 0.7,
            "max_tokens": 100
        },
    )
    return response.json()

# Example usage
models = get_models()
print("Available models:", models)

response = chat_with_shorthand_model()
print("Response from shorthand model:", response)

response = chat_with_raw_model_id()
print("Response from raw model ID:", response)
```

#### For Deployed API

```python
import requests
from typing import Dict, List, Any

api_key = "your-api-key"  # Use one of the API keys from your .env file
base_url = "https://triple3-chat-7lcftxblja-uc.a.run.app/api/v1"  # Replace with your actual service URL

# Get available models
def get_models() -> Dict[str, Any]:
    """Get a list of all available models from the API."""
    response = requests.get(
        f"{base_url}/models",
        headers={"Authorization": f"Bearer {api_key}"}
    )
    return response.json()

# Chat completion with shorthand model name
def chat_with_model(model: str, message: str) -> Dict[str, Any]:
    """Send a chat completion request to the API.

    Args:
        model: Either a shorthand model name or a raw model ID
        message: The message content to send

    Returns:
        The response from the API
    """
    response = requests.post(
        f"{base_url}/chat/completions",
        headers={"Authorization": f"Bearer {api_key}"},
        json={
            "model": model,
            "messages": [
                {"role": "user", "content": message}
            ],
            "temperature": 0.7,
            "max_tokens": 100
        },
    )
    return response.json()

# Example usage
print("Getting available models...")
models = get_models()
print(f"Found {len(models['models'])} models")

# Example 1: Using shorthand model name
print("\nTesting with shorthand model name...")
response1 = chat_with_model("gpt-4.1", "What model are you?")
print(f"Response from {response1['model']}:\n{response1['choices'][0]['message']['content']}")

# Example 2: Using raw model ID
print("\nTesting with raw model ID...")
response2 = chat_with_model("anthropic/claude-3.5-sonnet-20240620", "What model are you?")
print(f"Response from {response2['model']}:\n{response2['choices'][0]['message']['content']}")

```

### Python Streaming Examples

#### Streaming with requests

```python
import requests
import json

api_key = "test-api-key"  # Use the API key from your .env file
base_url = "http://localhost:8000/api/v1"

def stream_chat_completion(model: str, message: str):
    """Stream a chat completion response."""
    response = requests.post(
        f"{base_url}/chat/completions",
        headers={"Authorization": f"Bearer {api_key}"},
        json={
            "model": model,
            "messages": [
                {"role": "user", "content": message}
            ],
            "stream": True,
            "temperature": 0.7,
            "max_tokens": 500
        },
        stream=True  # Enable streaming in requests
    )

    # Process the streaming response
    for line in response.iter_lines():
        if line:
            line = line.decode('utf-8')
            if line.startswith('data: '):
                data = line[6:]  # Remove 'data: ' prefix
                if data == '[DONE]':
                    break
                try:
                    chunk = json.loads(data)
                    if chunk['choices'][0]['delta'].get('content'):
                        print(chunk['choices'][0]['delta']['content'], end='', flush=True)
                except json.JSONDecodeError:
                    continue
    print()  # New line at the end

# Example usage
print("Streaming response:")
stream_chat_completion("deepseek-v3", "Write a short poem about programming.")
```

#### Async streaming with aiohttp

```python
import aiohttp
import asyncio
import json

async def async_stream_chat_completion(model: str, message: str):
    """Async stream a chat completion response."""
    api_key = "test-api-key"
    base_url = "http://localhost:8000/api/v1"

    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{base_url}/chat/completions",
            headers={"Authorization": f"Bearer {api_key}"},
            json={
                "model": model,
                "messages": [
                    {"role": "user", "content": message}
                ],
                "stream": True,
                "temperature": 0.7,
                "max_tokens": 500
            }
        ) as response:
            async for line in response.content:
                line = line.decode('utf-8').strip()
                if line.startswith('data: '):
                    data = line[6:]  # Remove 'data: ' prefix
                    if data == '[DONE]':
                        break
                    try:
                        chunk = json.loads(data)
                        if chunk['choices'][0]['delta'].get('content'):
                            print(chunk['choices'][0]['delta']['content'], end='', flush=True)
                    except json.JSONDecodeError:
                        continue
            print()  # New line at the end

# Example usage
async def main():
    print("Async streaming response:")
    await async_stream_chat_completion("deepseek-v3", "Explain quantum computing in simple terms.")

# Run the async example
# asyncio.run(main())
```

### Available Models

The following models are available through the API:

- `claude-3-opus`: Anthropic's Claude 3 Opus
- `claude-3-sonnet`: Anthropic's Claude 3 Sonnet
- `claude-3-haiku`: Anthropic's Claude 3 Haiku
- `gpt-4o`: OpenAI's GPT-4o
- `gpt-4-turbo`: OpenAI's GPT-4 Turbo
- `gpt-3.5-turbo`: OpenAI's GPT-3.5 Turbo
- `mistral-large`: Mistral AI's Mistral Large
- `mixtral-8x7b`: Mistral AI's Mixtral 8x7B
- `deepseek-v3`: DeepSeek's Chat v3 (free tier)

## OpenRouter Setup

1. Sign up for an account at [OpenRouter](https://openrouter.ai/)
2. Create an API key in the dashboard
3. Add the API key to your `.env` file as `OPENROUTER_API_KEY`

OpenRouter is a unified API that provides access to various LLM models from different providers (OpenAI, Anthropic, Mistral, etc.) through a single API that's compatible with the OpenAI API format. This service uses the OpenAI client with OpenRouter's base URL to access these models.

## Deployment to Google Cloud Run

This project includes a deployment script that automates the process of deploying the API to Google Cloud Run with proper secret management.

### Prerequisites for Deployment

- Google Cloud SDK installed and configured
- Docker installed
- A Google Cloud project with the following APIs enabled:
  - Cloud Run API
  - Container Registry API
  - Secret Manager API
- Proper permissions to create and manage resources in your Google Cloud project

### Deployment Steps

1. Make sure your `.env` file is properly configured with your OpenRouter API key and other settings.

2. Run the deployment script:

```bash
./deploy.sh
```

The script will:
- Build a Docker image for the application
- Push the image to Google Container Registry
- Create a service account for the Cloud Run service
- Store your API keys securely in Google Cloud Secret Manager
- Deploy the application to Google Cloud Run
- Display the service URL

### Testing the Deployed API

The repository includes a test script (`test_api.sh`) that you can use to test the deployed API. This script tests both the health endpoint and the chat completions endpoint.

#### Prerequisites for Testing

- `jq` installed for parsing JSON responses (install with `brew install jq` on macOS or `apt-get install jq` on Ubuntu)

#### Running the Test Script

```bash
# Test only the health endpoint
./test_api.sh

# Test both health and chat completions endpoints
./test_api.sh your-api-key
```

Replace `your-api-key` with one of the API keys defined in your `.env` file.

#### What the Test Script Does

1. Tests the health endpoint (`/api/v1/health`) to verify that the API is running
2. If an API key is provided, tests the chat completions endpoint (`/api/v1/chat/completions`) with a simple message
3. Displays the request payload and response in a readable format

#### Customizing the Test Script

You can modify the `test_api.sh` script to change the API URL, request payload, or other parameters to suit your needs. The API URL is defined at the top of the script.

### Updating API Keys

If you need to update the API keys after deployment:

1. Update the API keys in your `.env` file
2. Run the deployment script again:

```bash
./deploy.sh
```

This will update the secrets in Google Cloud Secret Manager and redeploy the service.

### Security Best Practices

This project follows these security best practices for managing API keys and secrets:

1. **Environment Variables**: Sensitive values are stored in environment variables, not hardcoded in the source code.

2. **Secret Manager**: In production (Google Cloud Run), secrets are stored in Google Cloud Secret Manager and securely accessed at runtime.

3. **No Secrets in Images**: The Docker image does not contain any secrets or `.env` files.

4. **Version Control**: The `.env` file is excluded from version control via `.gitignore`.

5. **Least Privilege**: The service account has only the minimum necessary permissions to access the secrets it needs.

## Client Usage

Clients can use this API just like they would use the OpenAI API, with a few differences:

1. The base URL is your deployed API URL (or local server URL) instead of OpenAI's API URL
2. They need to use your API key with Bearer authentication in the `Authorization` header
3. The model names are simplified (e.g., `deepseek-v3` instead of `deepseek/deepseek-chat-v3-0324:free`)
4. Streaming is supported by setting `"stream": true` in the request body

### Streaming Support

The API fully supports OpenAI-compatible streaming responses:

- Set `"stream": true` in your request to enable streaming
- Responses are sent as Server-Sent Events (SSE) with `Content-Type: text/event-stream`
- Each chunk contains a `delta` object with incremental content
- The stream ends with a `data: [DONE]` message
- Compatible with OpenAI client libraries that support streaming
