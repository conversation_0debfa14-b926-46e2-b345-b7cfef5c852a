"""
Tests for the configuration module.
"""

import os
from unittest.mock import patch

import pytest

from chat.config import Settings


def test_settings_values():
    """Test that settings have the expected values."""
    settings = Settings()

    # Check API configuration
    assert settings.api_title == "RHCC Chatbot API"
    assert settings.api_description == "API for chatting with RHCC's LLM service"
    assert settings.api_version == "v1"
    assert settings.api_prefix == "/api/v1"
    # Debug can be True or False depending on the environment
    assert isinstance(settings.debug, bool)

    # Check authentication
    # API keys might be set in the .env file
    assert isinstance(settings.api_keys, list)

    # Check OpenRouter configuration
    assert settings.openrouter_base_url == "https://openrouter.ai/api/v1"
    # Default model might be set in the .env file or from the code
    assert isinstance(settings.default_model, str)
    # Check that models have been updated in the configuration
    assert isinstance(settings.available_models, dict)
    assert len(settings.available_models) > 0
    # Verify specific models exist
    assert "gpt-4o" in settings.available_models
    assert "gpt-4.1" in settings.available_models
    assert "claude-3.5-sonnet" in settings.available_models

    # Check that the model mappings are correct
    assert settings.available_models["gpt-4o"] == "openai/gpt-4o-2024-11-20"
    assert settings.available_models["gpt-4.1"] == "openai/gpt-4.1"

    # Check LLM configuration
    # System message might be customized in .env file
    assert isinstance(settings.system_message, str)
    assert len(settings.system_message) > 0
    # max_tokens might be set in .env file
    assert isinstance(settings.max_tokens, int)
    assert settings.temperature == 0.7

    # Check rate limiting
    assert settings.rate_limit_requests == 100
    assert settings.rate_limit_period == 60


def test_settings_from_env_vars():
    """Test that settings can be loaded from environment variables."""
    # Set environment variables
    env_vars = {
        "API_TITLE": "Test API",
        "API_DESCRIPTION": "Test description",
        "API_VERSION": "v2",
        "API_PREFIX": "/api/v2",
        "DEBUG": "true",
        "API_KEYS": '["test-key-1", "test-key-2"]',
        "OPENROUTER_API_KEY": "test-api-key",
        "OPENROUTER_BASE_URL": "https://test.openrouter.ai/api/v1",
        "DEFAULT_MODEL": "test-model",
        "SYSTEM_MESSAGE": "Test system message",
        "MAX_TOKENS": "2000",
        "TEMPERATURE": "0.5",
        "RATE_LIMIT_REQUESTS": "50",
        "RATE_LIMIT_PERIOD": "30",
    }

    with patch.dict(os.environ, env_vars):
        settings = Settings()

        # Check API configuration
        assert settings.api_title == "Test API"
        assert settings.api_description == "Test description"
        assert settings.api_version == "v2"
        assert settings.api_prefix == "/api/v2"
        assert settings.debug is True

        # Check authentication
        assert settings.api_keys == ["test-key-1", "test-key-2"]

        # Check OpenRouter configuration
        assert settings.openrouter_api_key == "test-api-key"
        assert settings.openrouter_base_url == "https://test.openrouter.ai/api/v1"
        assert settings.default_model == "test-model"

        # Check LLM configuration
        assert settings.system_message == "Test system message"
        assert settings.max_tokens == 2000
        assert settings.temperature == 0.5

        # Check rate limiting
        assert settings.rate_limit_requests == 50
        assert settings.rate_limit_period == 30


def test_settings_precedence():
    """Test that environment variables take precedence over default values."""
    # Test that environment variables override defaults
    env_vars = {
        "API_TITLE": "Environment API",
        "OPENROUTER_API_KEY": "environment-api-key",
        "DEFAULT_MODEL": "environment-model",
    }

    with patch.dict(os.environ, env_vars):
        settings = Settings()

        # Check that environment variables override defaults
        assert settings.api_title == "Environment API"  # From env vars
        assert settings.openrouter_api_key == "environment-api-key"  # From env vars
        assert settings.default_model == "environment-model"  # From env vars

        # Check that unset values use defaults
        assert (
            settings.api_description == "API for chatting with RHCC's LLM service"
        )  # Default
        assert settings.api_version == "v1"  # Default
        # max_tokens might be set in .env file
        assert isinstance(settings.max_tokens, int)
        assert settings.temperature == 0.7  # Default
