"""
Tests for the chatbot service.
"""

import uuid
from unittest.mock import AsyncMock, patch

import pytest

from chat.models import (
    ChatCompletionChoice,
    ChatCompletionRequest,
    ChatCompletionResponse,
    Message,
    Role,
    Usage,
)
from chat.service import ChatbotService


@pytest.mark.asyncio
async def test_generate_chat_completion_with_mocked_service():
    """
    Test generating a chat completion by mocking the service method.

    This test mocks the entire service method to focus on testing
    the interface rather than the implementation details.
    """
    # Create a request
    request = ChatCompletionRequest(
        model="test-model",
        messages=[
            Message(role=Role.USER, content="Hello, how are you?"),
        ],
        temperature=0.7,
    )

    # Create expected response
    expected_response = ChatCompletionResponse(
        id=f"chatcmpl-{uuid.uuid4()}",
        model="test-model",
        choices=[
            ChatCompletionChoice(
                index=0,
                message=Message(
                    role=Role.ASSISTANT,
                    content="This is a test response.",
                ),
                finish_reason="stop",
            )
        ],
        usage=Usage(
            prompt_tokens=10,
            completion_tokens=10,
            total_tokens=20,
        ),
    )

    # Mock the entire service method
    with patch.object(
        ChatbotService,
        "generate_chat_completion",
        new=AsyncMock(return_value=expected_response),
    ) as mock_generate:
        # Call the service
        response = await ChatbotService.generate_chat_completion(request)

        # Check that the mock was called with the correct parameters
        mock_generate.assert_called_once_with(request)

        # Check the response
        assert response.model == "test-model"
        assert len(response.choices) == 1
        assert response.choices[0].message.role == Role.ASSISTANT
        assert response.choices[0].message.content == "This is a test response."
        assert response.choices[0].finish_reason == "stop"
        assert response.usage.prompt_tokens == 10
        assert response.usage.completion_tokens == 10
        assert response.usage.total_tokens == 20


@pytest.mark.asyncio
async def test_generate_chat_completion_with_stream_false():
    """Test that non-streaming requests work correctly."""
    request = ChatCompletionRequest(
        model="test-model",
        messages=[
            Message(role=Role.USER, content="Hello, how are you?"),
        ],
        stream=False,  # Explicitly set to False
        temperature=0.7,
    )

    expected_response = ChatCompletionResponse(
        id=f"chatcmpl-{uuid.uuid4()}",
        model="test-model",
        choices=[
            ChatCompletionChoice(
                index=0,
                message=Message(
                    role=Role.ASSISTANT,
                    content="This is a test response.",
                ),
                finish_reason="stop",
            )
        ],
        usage=Usage(
            prompt_tokens=10,
            completion_tokens=10,
            total_tokens=20,
        ),
    )

    with patch.object(
        ChatbotService,
        "generate_chat_completion",
        new=AsyncMock(return_value=expected_response),
    ) as mock_generate:
        response = await ChatbotService.generate_chat_completion(request)

        mock_generate.assert_called_once_with(request)
        assert response.model == "test-model"
        assert response.choices[0].message.content == "This is a test response."
