"""
Pytest configuration and fixtures.
"""

import os
import pytest
from fastapi.testclient import TestClient

from chat.main import app


@pytest.fixture(scope="session", autouse=True)
def disable_langsmith():
    """Disable LangSmith tracing for all tests to avoid logging warnings."""
    os.environ["LANGCHAIN_TRACING_V2"] = "false"
    os.environ["LANGSMITH_TRACING"] = "false"
    yield


@pytest.fixture
def client():
    """Create a test client for the API."""
    return TestClient(app)
