[tool.poetry]
name = "chat"
version = "0.1.0"
description = "Chat package for triple3-chat"
authors = [
    "Your Name <<EMAIL>>"
]
readme = "README.md"
packages = [{include = "chat", from = "src"}]

[tool.poetry.dependencies]
python = ">=3.10,<4.0"
langchain = "^0.1.0"
langchain-openai = "^0.0.5"
langchain-anthropic = "^0.1.1"
langchain-google-genai = "^0.0.6"
langchain-community = "^0.0.13"
fastapi = "^0.110.0"
uvicorn = "^0.27.0"
pydantic = "^2.6.0"
python-dotenv = "^1.0.0"
pydantic-settings = "^2.8.1"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
black = "^24.2.0"
isort = "^5.13.2"
nbqa = "^1.9.1"
pre-commit = "^4.2.0"
pytest-asyncio = "^0.26.0"
pytest-cov = "^6.1.1"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ["py310"]

[tool.isort]
profile = "black"
line_length = 88
known_first_party = ["chat"]

[tool.pytest.ini_options]
asyncio_mode = "strict"
asyncio_default_fixture_loop_scope = "function"
