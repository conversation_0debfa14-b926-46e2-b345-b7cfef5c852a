# Git
.git
.gitignore
.github

# Docker
Dockerfile
.dockerignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.env
.venv
venv/
ENV/

# IDE
.idea
.vscode
*.swp
*.swo

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/

# Logs
log/
*.log

# Local development
.DS_Store
