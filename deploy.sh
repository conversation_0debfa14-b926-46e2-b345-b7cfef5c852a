#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status

# Configuration
PROJECT_ID=$(gcloud config get-value project)
IMAGE_NAME="triple3-chat"
REGION="us-central1"
SERVICE_NAME="triple3-chat"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting deployment process for ${SERVICE_NAME}...${NC}"

# Step 1: Build the Docker image
echo -e "${YELLOW}Step 1: Building Docker image...${NC}"
docker build --platform linux/amd64 -t ${IMAGE_NAME} .
if [ $? -ne 0 ]; then
    echo -e "${RED}Docker build failed. Exiting.${NC}"
    exit 1
fi
echo -e "${GREEN}Docker image built successfully.${NC}"

# Step 2: Tag the image for Google Container Registry
echo -e "${YELLOW}Step 2: Tagging image for Google Container Registry...${NC}"
docker tag ${IMAGE_NAME} gcr.io/${PROJECT_ID}/${IMAGE_NAME}:latest
if [ $? -ne 0 ]; then
    echo -e "${RED}Docker tag failed. Exiting.${NC}"
    exit 1
fi
echo -e "${GREEN}Image tagged successfully.${NC}"

# Step 3: Configure Docker to use gcloud as a credential helper
echo -e "${YELLOW}Step 3: Configuring Docker to use gcloud credentials...${NC}"
gcloud auth configure-docker --quiet
if [ $? -ne 0 ]; then
    echo -e "${RED}Docker credential configuration failed. Exiting.${NC}"
    exit 1
fi
echo -e "${GREEN}Docker credentials configured successfully.${NC}"

# Step 4: Push the image to Google Container Registry
echo -e "${YELLOW}Step 4: Pushing image to Google Container Registry...${NC}"
docker push gcr.io/${PROJECT_ID}/${IMAGE_NAME}:latest
if [ $? -ne 0 ]; then
    echo -e "${RED}Docker push failed. Exiting.${NC}"
    exit 1
fi
echo -e "${GREEN}Image pushed to Google Container Registry successfully.${NC}"

# Step 5: Create a service account for the Cloud Run service
echo -e "${YELLOW}Step 5: Creating service account...${NC}"
SERVICE_ACCOUNT="${SERVICE_NAME}-sa@${PROJECT_ID}.iam.gserviceaccount.com"

# Check if the service account already exists
if gcloud iam service-accounts describe ${SERVICE_ACCOUNT} --project=${PROJECT_ID} &>/dev/null; then
    echo -e "${GREEN}Service account ${SERVICE_ACCOUNT} already exists.${NC}"
else
    echo -e "${YELLOW}Creating service account ${SERVICE_ACCOUNT}...${NC}"
    gcloud iam service-accounts create ${SERVICE_NAME}-sa \
        --display-name="Service account for ${SERVICE_NAME}" \
        --project=${PROJECT_ID}

    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to create service account. Exiting.${NC}"
        exit 1
    fi
    echo -e "${GREEN}Service account created successfully.${NC}"
fi

# Step 6: Create or update secrets in Secret Manager
echo -e "${YELLOW}Step 6: Managing secrets in Secret Manager...${NC}"

# Get the OpenRouter API key from the .env file
OPENROUTER_API_KEY=$(grep OPENROUTER_API_KEY .env | cut -d '=' -f2 | tr -d '"')
if [ -z "$OPENROUTER_API_KEY" ]; then
    echo -e "${RED}OpenRouter API key not found in .env file. Please add it.${NC}"
    exit 1
fi

# Get the OpenRouter base URL from the .env file
OPENROUTER_BASE_URL=$(grep OPENROUTER_BASE_URL .env | cut -d '=' -f2 | tr -d '"')
if [ -z "$OPENROUTER_BASE_URL" ]; then
    echo -e "${YELLOW}OpenRouter base URL not found in .env file. Using default.${NC}"
    OPENROUTER_BASE_URL="https://openrouter.ai/api/v1"
fi

# Get the default model from the .env file
DEFAULT_MODEL=$(grep DEFAULT_MODEL .env | cut -d '=' -f2 | tr -d '"')
if [ -z "$DEFAULT_MODEL" ]; then
    echo -e "${YELLOW}Default model not found in .env file. Using default.${NC}"
    DEFAULT_MODEL="gpt-4.1"
fi

# Get the API keys from the .env file
API_KEYS=$(grep API_KEYS .env | cut -d '=' -f2 | tr -d ' ' | tr -d '\n' | tr -d '\r')
if [ -z "$API_KEYS" ]; then
    echo -e "${YELLOW}API keys not found in .env file. Using default test-api-key.${NC}"
    API_KEYS='["test-api-key"]'
fi

# Clean up the API_KEYS value to ensure it's valid JSON
API_KEYS=$(echo "$API_KEYS" | sed 's/\\//g')

# Validate that API_KEYS is valid JSON
if ! echo "$API_KEYS" | jq . &>/dev/null; then
    echo -e "${RED}API_KEYS is not valid JSON. Please check your .env file.${NC}"
    echo -e "${YELLOW}Current value: $API_KEYS${NC}"
    echo -e "${YELLOW}Example of valid format: [\"key1\", \"key2\"]${NC}"
    exit 1
fi

# Create or update the secrets
echo -e "${YELLOW}Creating/updating OpenRouter API key secret...${NC}"
gcloud secrets create openrouter-api-key --project=${PROJECT_ID} --replication-policy="automatic" --data-file=<(echo -n "$OPENROUTER_API_KEY") --quiet 2>/dev/null || \
gcloud secrets versions add openrouter-api-key --project=${PROJECT_ID} --data-file=<(echo -n "$OPENROUTER_API_KEY") --quiet

echo -e "${YELLOW}Creating/updating API keys secret...${NC}"
gcloud secrets create api-keys --project=${PROJECT_ID} --replication-policy="automatic" --data-file=<(echo -n "$API_KEYS") --quiet 2>/dev/null || \
gcloud secrets versions add api-keys --project=${PROJECT_ID} --data-file=<(echo -n "$API_KEYS") --quiet

# Grant the service account access to the secrets
echo -e "${YELLOW}Granting service account access to secrets...${NC}"
gcloud secrets add-iam-policy-binding openrouter-api-key \
    --project=${PROJECT_ID} \
    --member="serviceAccount:${SERVICE_ACCOUNT}" \
    --role="roles/secretmanager.secretAccessor" \
    --quiet

gcloud secrets add-iam-policy-binding api-keys \
    --project=${PROJECT_ID} \
    --member="serviceAccount:${SERVICE_ACCOUNT}" \
    --role="roles/secretmanager.secretAccessor" \
    --quiet

# Step 7: Deploy to Google Cloud Run with secret references and environment variables
echo -e "${YELLOW}Step 7: Deploying to Google Cloud Run...${NC}"
gcloud run deploy ${SERVICE_NAME} \
    --image gcr.io/${PROJECT_ID}/${IMAGE_NAME}:latest \
    --platform managed \
    --region ${REGION} \
    --allow-unauthenticated \
    --set-env-vars="OPENROUTER_BASE_URL=${OPENROUTER_BASE_URL},DEFAULT_MODEL=${DEFAULT_MODEL}" \
    --set-secrets="OPENROUTER_API_KEY=openrouter-api-key:latest,API_KEYS=api-keys:latest" \
    --service-account="${SERVICE_NAME}-sa@${PROJECT_ID}.iam.gserviceaccount.com"
if [ $? -ne 0 ]; then
    echo -e "${RED}Deployment to Cloud Run failed. Exiting.${NC}"
    exit 1
fi
echo -e "${GREEN}Deployment to Cloud Run completed successfully.${NC}"

# Step 8: Get the deployed service URL
echo -e "${YELLOW}Step 8: Getting service URL...${NC}"
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --platform managed --region ${REGION} --format 'value(status.url)')
echo -e "${GREEN}Service deployed successfully!${NC}"
echo -e "${GREEN}Service URL: ${SERVICE_URL}${NC}"
echo -e "${YELLOW}Test the API with: curl ${SERVICE_URL}/api/v1/health${NC}"

echo -e "${GREEN}Deployment process completed successfully!${NC}"
