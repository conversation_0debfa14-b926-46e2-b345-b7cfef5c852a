"""
Pydantic models for the chatbot API.

These models follow the OpenAI API format for compatibility.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional

from pydantic import BaseModel, Field


class Role(str, Enum):
    """Message role types."""

    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    FUNCTION = "function"


class Message(BaseModel):
    """Conversation message."""

    role: Role
    content: str
    name: Optional[str] = None


class ChatCompletionRequest(BaseModel):
    """Chat completion request (OpenAI format)."""

    model: str
    messages: List[Message]
    temperature: Optional[float] = 0.7
    top_p: Optional[float] = 1.0
    n: Optional[int] = 1
    stream: Optional[bool] = False
    max_tokens: Optional[int] = None
    presence_penalty: Optional[float] = 0
    frequency_penalty: Optional[float] = 0
    user: Optional[str] = None


class ChatCompletionChoice(BaseModel):
    """Chat completion choice."""

    index: int
    message: Message
    finish_reason: Optional[str] = None


class Usage(BaseModel):
    """Token usage stats."""

    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class ChatCompletionResponse(BaseModel):
    """Chat completion response (OpenAI format)."""

    id: str
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(datetime.now().timestamp()))
    model: str
    choices: List[ChatCompletionChoice]
    usage: Usage


class ErrorResponse(BaseModel):
    """API error response."""

    error: Dict[str, str]


class HealthResponse(BaseModel):
    """Health check response."""

    status: str = "ok"
    version: str


# Streaming response models
class Delta(BaseModel):
    """Delta content for streaming responses."""

    role: Optional[Role] = None
    content: Optional[str] = None


class ChatCompletionStreamChoice(BaseModel):
    """Chat completion choice for streaming responses."""

    index: int
    delta: Delta
    finish_reason: Optional[str] = None


class ChatCompletionStreamResponse(BaseModel):
    """Chat completion streaming response (OpenAI format)."""

    id: str
    object: str = "chat.completion.chunk"
    created: int = Field(default_factory=lambda: int(datetime.now().timestamp()))
    model: str
    choices: List[ChatCompletionStreamChoice]
