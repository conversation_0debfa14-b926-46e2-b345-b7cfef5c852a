"""
Core chatbot service implementation.
"""

import logging
import uuid
from typing import Any, AsyncGenerator, List, Optional, Union

from langchain_core.outputs import Generation, LLMResult

from chat.llm import create_langchain_messages, get_chat_model, stream_chat_model
from chat.models import (
    ChatCompletionChoice,
    ChatCompletionRequest,
    ChatCompletionResponse,
    ChatCompletionStreamChoice,
    ChatCompletionStreamResponse,
    Delta,
    Message,
    Role,
    Usage,
)

logger = logging.getLogger(__name__)


class ChatbotService:
    """Service for handling chatbot interactions."""

    @staticmethod
    async def generate_chat_completion(
        request: ChatCompletionRequest,
    ) -> ChatCompletionResponse:
        """Generate a chat completion response."""
        try:
            messages_dict = [msg.model_dump() for msg in request.messages]
            langchain_messages = create_langchain_messages(messages_dict)

            chat_model, token_usage_handler = get_chat_model(
                model_name=request.model,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
            )

            ai_message = await chat_model.agenerate(messages=[langchain_messages])
            generation = ai_message.generations[0][0]

            # Extract the actual content from the generation using a more robust approach
            # that works with different LLM models and their output formats
            response_text = ""

            # Case 1: ChatOpenAI and similar models that use AIMessage format
            if hasattr(generation, "message"):
                msg = getattr(generation, "message")
                if hasattr(msg, "content"):
                    response_text = getattr(msg, "content", "")
            # Case 2: Models that directly provide text attribute
            elif hasattr(generation, "text"):
                response_text = getattr(generation, "text", "")
            # Case 3: Models that use different attributes (using getattr for type safety)
            elif hasattr(generation, "output_text"):
                response_text = getattr(generation, "output_text", "")
            # Case 4: Fall back to string representation but clean it up
            else:
                raw_text = str(generation)
                # Try to extract content from the string representation
                if "content=" in raw_text:
                    # Handle both single and double quote formats
                    if 'content="' in raw_text:
                        response_text = raw_text.split('content="')[1].split('"')[0]
                    elif "content='" in raw_text:
                        response_text = raw_text.split("content='")[1].split("'")[0]
                else:
                    response_text = raw_text

            choice = ChatCompletionChoice(
                index=0,
                message=Message(
                    role=Role.ASSISTANT,
                    content=response_text,
                ),
                finish_reason="stop",
            )

            usage = Usage(
                prompt_tokens=token_usage_handler.prompt_tokens,
                completion_tokens=token_usage_handler.completion_tokens,
                total_tokens=token_usage_handler.total_tokens,
            )

            return ChatCompletionResponse(
                id=f"chatcmpl-{uuid.uuid4()}",
                model=request.model,
                choices=[choice],
                usage=usage,
            )
        except Exception as e:
            logger.exception(f"Error generating chat completion: {e}")
            raise

    @staticmethod
    async def stream_chat_completion(
        request: ChatCompletionRequest,
    ) -> AsyncGenerator[ChatCompletionStreamResponse, None]:
        """Generate a streaming chat completion response."""
        try:
            messages_dict = [msg.model_dump() for msg in request.messages]
            langchain_messages = create_langchain_messages(messages_dict)

            # Generate a unique ID for this completion
            completion_id = f"chatcmpl-{uuid.uuid4()}"

            # Send the initial chunk with role information
            initial_choice = ChatCompletionStreamChoice(
                index=0,
                delta=Delta(role=Role.ASSISTANT),
                finish_reason=None,
            )

            initial_response = ChatCompletionStreamResponse(
                id=completion_id,
                model=request.model,
                choices=[initial_choice],
            )

            yield initial_response

            # Stream content chunks
            async for content_chunk in stream_chat_model(
                model_name=request.model,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                messages=langchain_messages,
            ):
                if content_chunk:  # Only yield non-empty chunks
                    choice = ChatCompletionStreamChoice(
                        index=0,
                        delta=Delta(content=content_chunk),
                        finish_reason=None,
                    )

                    response = ChatCompletionStreamResponse(
                        id=completion_id,
                        model=request.model,
                        choices=[choice],
                    )

                    yield response

            # Send the final chunk with finish_reason
            final_choice = ChatCompletionStreamChoice(
                index=0,
                delta=Delta(),
                finish_reason="stop",
            )

            final_response = ChatCompletionStreamResponse(
                id=completion_id,
                model=request.model,
                choices=[final_choice],
            )

            yield final_response

        except Exception as e:
            logger.exception(f"Error generating streaming chat completion: {e}")
            raise
