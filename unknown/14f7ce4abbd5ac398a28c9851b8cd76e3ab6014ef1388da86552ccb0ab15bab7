"""
Tests for the chatbot API.
"""

from unittest.mock import AsyncMock, patch

import pytest
from fastapi.testclient import Test<PERSON>lient

from chat.api import ModelDetails
from chat.config import settings
from chat.main import app
from chat.models import (
    ChatCompletionChoice,
    ChatCompletionResponse,
    Message,
    Role,
    Usage,
)


@pytest.fixture
def client():
    """Create a test client for the API."""
    return TestClient(app)


@pytest.fixture
def mock_generate_chat_completion():
    """Mock the generate_chat_completion method."""
    # Create a mock response
    mock_response = ChatCompletionResponse(
        id="chatcmpl-123",
        model="test-model",
        choices=[
            ChatCompletionChoice(
                index=0,
                message=Message(
                    role=Role.ASSISTANT,
                    content="This is a test response.",
                ),
                finish_reason="stop",
            )
        ],
        usage=Usage(
            prompt_tokens=10,
            completion_tokens=10,
            total_tokens=20,
        ),
    )

    # Create an async mock that returns the response
    async_mock = AsyncMock(return_value=mock_response)

    # Patch the method
    with patch("chat.service.ChatbotService.generate_chat_completion", async_mock):
        yield async_mock


def test_health(client):
    """Test the health endpoint."""
    response = client.get(f"{settings.api_prefix}/health")
    assert response.status_code == 200
    assert response.json()["status"] == "ok"
    assert response.json()["version"] == settings.api_version


def test_chat_completion_with_auth(client, mock_generate_chat_completion):
    """Test the chat completion endpoint with authentication."""
    # Set up test data
    request_data = {
        "model": "test-model",
        "messages": [{"role": "user", "content": "Hello, how are you?"}],
        "temperature": 0.7,
    }

    # Configure API keys and available models for testing
    with (
        patch("chat.config.settings.api_keys", ["test-api-key"]),
        patch("chat.config.settings.available_models", {"test-model": "test-model"}),
    ):
        # Make the request with Bearer authentication
        response = client.post(
            f"{settings.api_prefix}/chat/completions",
            json=request_data,
            headers={"Authorization": "Bearer test-api-key"},
        )

        # Check the response
        assert response.status_code == 200
        assert response.json()["model"] == "test-model"
        assert len(response.json()["choices"]) == 1
        assert (
            response.json()["choices"][0]["message"]["content"]
            == "This is a test response."
        )
        assert response.json()["choices"][0]["message"]["role"] == "assistant"

        # Check that the mock was called
        mock_generate_chat_completion.assert_called_once()


def test_chat_completion_without_auth(client):
    """Test the chat completion endpoint without authentication."""
    # Set up test data
    request_data = {
        "model": "test-model",
        "messages": [{"role": "user", "content": "Hello, how are you?"}],
    }

    # Configure API keys for testing
    with (
        patch("chat.config.settings.api_keys", ["test-api-key"]),
        patch("chat.config.settings.available_models", {"test-model": "test-model"}),
    ):
        # Make the request without authentication
        response = client.post(
            f"{settings.api_prefix}/chat/completions",
            json=request_data,
        )

        # Check that authentication failed
        assert response.status_code == 401
        assert "Invalid or missing API key" in response.json()["detail"]


def test_chat_completion_with_invalid_auth(client):
    """Test the chat completion endpoint with invalid authentication."""
    # Set up test data
    request_data = {
        "model": "test-model",
        "messages": [{"role": "user", "content": "Hello, how are you?"}],
    }

    # Configure API keys for testing
    with (
        patch("chat.config.settings.api_keys", ["test-api-key"]),
        patch("chat.config.settings.available_models", {"test-model": "test-model"}),
    ):
        # Make the request with invalid authentication
        response = client.post(
            f"{settings.api_prefix}/chat/completions",
            json=request_data,
            headers={"Authorization": "Bearer invalid-api-key"},
        )

        # Check that authentication failed
        assert response.status_code == 401
        assert "Invalid or missing API key" in response.json()["detail"]


@pytest.fixture
def mock_get_available_models():
    """Mock the get_available_models function."""
    # Create mock model details
    mock_models = {
        "gpt-4.1": {
            "id": "openai/gpt-4.1",
            "provider": "openai",
            "shorthand": "gpt-4.1",
            "supports_streaming": True,
        },
        "claude-3.5-sonnet": {
            "id": "anthropic/claude-3.5-sonnet-20240620",
            "provider": "anthropic",
            "shorthand": "claude-3.5-sonnet",
            "supports_streaming": True,
        },
    }

    # Create a mock that returns the models
    with patch("chat.llm.get_available_models", return_value=mock_models):
        yield


def test_list_models(client, mock_get_available_models):
    """Test the models endpoint."""
    # Configure API keys for testing
    with patch("chat.config.settings.api_keys", ["test-api-key"]):
        # Make the request with authentication
        response = client.get(
            f"{settings.api_prefix}/models",
            headers={"Authorization": "Bearer test-api-key"},
        )

        # Check the response
        assert response.status_code == 200
        data = response.json()
        assert "models" in data
        assert len(data["models"]) > 0

        # Check that required models are in the response
        # The actual models available might change, so we only check for specific model formats
        # rather than specific model names
        openai_models = [
            model
            for model in data["models"]
            if data["models"][model]["provider"] == "openai"
        ]
        assert len(openai_models) > 0, "Should have at least one OpenAI model"

        # Verify at least one model from another provider exists
        non_openai_models = [
            model
            for model in data["models"]
            if data["models"][model]["provider"] != "openai"
        ]
        assert len(non_openai_models) > 0, "Should have at least one non-OpenAI model"

        # Verify model structure by checking any model
        # Get the first model from the list
        first_model_name = list(data["models"].keys())[0]
        first_model = data["models"][first_model_name]

        # Verify the model structure
        assert "id" in first_model
        assert "provider" in first_model
        assert "shorthand" in first_model
        assert "supports_streaming" in first_model

        # Verify the provider is extracted correctly from the ID
        assert first_model["provider"] in first_model["id"]


def test_list_models_without_auth(client):
    """Test the models endpoint without authentication."""
    # Configure API keys for testing
    with patch("chat.config.settings.api_keys", ["test-api-key"]):
        # Make the request without authentication
        response = client.get(f"{settings.api_prefix}/models")

        # Check that authentication failed
        assert response.status_code == 401
        assert "Invalid or missing API key" in response.json()["detail"]
